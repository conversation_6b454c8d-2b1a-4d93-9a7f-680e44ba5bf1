<template>
    <proofread-task-check
        api-path="mang/nurse"
        title="服务项目校对"
        :work-id="workId"
        :month="month"
    />
</template>

<script>
import ProofreadTaskCheck from '@/pagesA/care/components/proofread-task-check.vue'

const app = getApp()

export default {
    components: {
        ProofreadTaskCheck,
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            workId: '',
            month: null,
            colors: '',
        }
    },
    onLoad(options) {
        this.workId = options.workId ?? ''
        this.month = options.month
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
    },
}
</script>
