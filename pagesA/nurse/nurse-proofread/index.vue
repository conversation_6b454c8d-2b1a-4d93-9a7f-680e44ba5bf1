<template>
    <proofread-task-list
        ref="proofreadTaskList"
        api-path="mang/nurse"
        check-page-path="/pagesA/nurse/nurse-proofread/check"
        title="校对任务列表"
    />
</template>

<script>
import ProofreadTaskList from '@/pagesA/care/components/proofread-task-list.vue'

const app = getApp()

export default {
    components: {
        ProofreadTaskList,
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
        }
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        // 通知子组件刷新数据
        this.$refs.proofreadTaskList && this.$refs.proofreadTaskList.downCallback()
    },
}
</script>
