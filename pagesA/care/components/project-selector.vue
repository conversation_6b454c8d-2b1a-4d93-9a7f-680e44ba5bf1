<template>
    <view>
        <scroll-view scroll-y="true" style="max-height: 60vh;">
            <u-checkbox-group v-model="checkboxValue" placement="column">
                <view v-for="(item, index) in projectData"
                    :key="index"
                    class="flex justify-between align-center padding-lr padding-tb-sm solid-bottom">
                    <view class="flex-sub">
                        <view class="text-df text-bold text-black">
                            {{ item.name }}
                            <text class="text-sm text-gray">(编号：{{ item.govCode }})</text>
                        </view>
                        <view class="text-xs text-gray margin-top-xs">
                            <text>{{ item.projectTypeName }}</text>
                            <text v-if="item.minDuration || item.maxDuration" class="margin-left-sm">
                                <text v-if="item.minDuration">{{ item.minDuration }}</text>
                                <text v-if="item.minDuration && item.maxDuration">--</text>
                                <text v-if="item.maxDuration">{{ item.maxDuration }}</text>
                                分钟
                            </text>
                        </view>
                    </view>
                    <view class="flex-sub-0">
                        <u-checkbox
                            v-model="item.checked"
                            :activeColor="colors"
                            size="big"
                            :name="item.id"></u-checkbox>
                    </view>
                </view>
            </u-checkbox-group>
        </scroll-view>
        <view class="padding margin-tb-xs">
            <u-button
                type="primary"
                shape="circle"
                :color="colors"
                text="选择选中服务项目"
                size="normal"
                :disabled="!checkboxValue.length"
                @click="handleSave"
            ></u-button>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex'

const app = getApp()

export default {
    props: {
        projectData: {
            type: Array,
            default: () => [],
        },
        selectedProjects: {
            type: Array,
            default: () => [],
        },
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
            checkboxValue: [],
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
        }),
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
    },
    mounted() {
        this.initCheckboxValue()
    },
    watch: {
        projectData: {
            handler() {
                this.initCheckboxValue()
            },
            deep: true,
        },
        selectedProjects: {
            handler() {
                this.initCheckboxValue()
            },
            deep: true,
        },
    },
    methods: {
        initCheckboxValue() {
            this.checkboxValue = []
            if (this.projectData && this.projectData.length) {
                this.projectData.forEach(item => {
                    const isSelected = this.selectedProjects.some((selected) =>
                        selected.projectId === item.id || selected.id === item.id,
                    )
                    this.$set(item, 'checked', isSelected)
                    if (isSelected) {
                        this.checkboxValue.push(item.id)
                    }
                })
            }
        },
        handleSave() {
            if (!this.checkboxValue || !this.checkboxValue.length) return
            let selectedProjects = []
            this.checkboxValue.forEach(projectId => {
                let project = this.projectData.find(p => p.id === projectId)
                if (project) {
                    selectedProjects.push(project)
                }
            })
            this.$emit('selectProject', selectedProjects)
        },
    },
}
</script>

<style lang="scss" scoped>
:deep(.u-checkbox__icon-wrap) {
  width: 48rpx;
  height: 48rpx;

  span {
    font-size: 40rpx;
  }
}
</style>
