export default {
	isAdmin:{
		url: '/permission/mobile/isAdmin',
		auth: true,
		method: 'GET',
	},
	isManager: {
		url: '/permission/mobile/isManagerOrStaff',
		auth: true,
		method: 'GET',
	},
	community:{
		my:{
			url: '/homecare/base/mobile/myCommunity_listpg',
			auth: true,
			method: 'GET',
		},
	},
	myMenu:{
		url: '/permission/mobile/mobileRouter_list',
		auth: true,
		method: 'GET',
	},
	nurse:{
		menuCount:{
			url: '/homecare/base/mobileNurse/menuCount',
			auth: true,
			method: 'GET',
		},
		customer:{
			listpg:{
				url: '/homecare/base/mobileNurse/alreadySetAttendant_customer_listpg',
				auth: true,
				method: 'GET',
			},
			info:{
				url: '/homecare/base/mobileNurse/customer_info',
				auth: true,
				method: 'GET',
			},
			fileData:{
				allList:{
					url: '/homecare/base/mobileNurse/data_allList',
					auth: true,
					method: 'GET',
				}
			},
		},
		attendant:{
			listpg:{
				url: '/homecare/base/mobileNurse/attendant_listpg',
				auth: true,
				method: 'GET',
			},
			customer:{
				locationAll:{
					url: '/homecare/base/mobileNurse/attendant_all_location_info',
					auth: true,
					method: 'GET',
				}
			}
		},
		setAttendant:{
			listpg:{
				url: '/homecare/base/mobileNurse/waitSetAttendant_customer_listpg',
				auth: true,
				method: 'GET',
			},
			set:{
				url: '/homecare/base/mobileNurse/setAttendant',
				auth: true,
				method: 'POST',
			},
			cancel:{
				url: '/homecare/base/mobileNurse/cancelAttendant',
				auth: true,
				method: 'POST',
			},
			change:{
				url: '/homecare/base/mobileNurse/changeAttendant',
				auth: true,
				method: 'POST',
			},

		},
		first:{
			listpg:{
				url: '/homecare/work/mobileNurse/first_person_listpg',
				auth: true,
				method: 'GET',
			},
			info:{
				url: '/homecare/work/mobileNurse/first_person_info',
				auth: true,
				method: 'GET',
			},
			submit:{
				url: '/homecare/work/mobileNurse/first_submit',
				auth: true,
				method: 'POST',
			},
			fileData:{
				allList:{
					url: '/homecare/work/mobileNurse/first_data_allList',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/first_data_save',
					auth: true,
					method: 'POST',
				}
			},
			state_info:{
				url: '/homecare/work/mobileNurse/first_state_info',
				auth: true,
				method: 'GET',
			},
			checkin:{
				save:{
					url: '/homecare/work/mobileNurse/first_checkin',
					auth: true,
					method: 'POST',
				},
				info:{
					url: '/homecare/work/mobileNurse/first_checkin_info',
					auth: true,
					method: 'GET',
				},
				range:{
					url: '/homecare/work/mobileNurse/first_checkin_range',
					auth: true,
					method: 'POST',
				},

			},
			scene:{
				setInfo:{
					url: '/homecare/work/mobileNurse/first_checkin_img_setInfo',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/first_checkin_img_save',
					auth: true,
					method: 'POST',
				},
				list:{
					url: '/homecare/work/mobileNurse/first_checkin_img_list',
					auth: true,
					method: 'GET',
				}
			},
			imgHead:{
				info:{
					url: '/homecare/work/mobileNurse/first_customer_imgHead_info',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/first_customer_imgHead',
					auth: true,
					method: 'POST',
				},
			},
			project:{
				allList:{
					url: '/homecare/work/mobileNurse/project_listByFirstId',
					auth: true,
					method: 'GET',
				},
				listpg:{
					url: '/homecare/work/mobileNurse/first_project_listpg',
					auth: true,
					method: 'GET',
				},
				delete:{
					url: '/homecare/work/mobileNurse/first_project_delete',
					auth: true,
					method: 'POST',
				},
				add:{
					url: '/homecare/work/mobileNurse/first_project_add',
					auth: true,
					method: 'POST',
				},
				save:{
					url: '/homecare/work/mobileNurse/first_project_saveAll',
					auth: true,
					method: 'POST',
				},
				time:{
					url: '/homecare/work/mobileNurse/first_project_saveTimes',
					auth: true,
					method: 'POST',
				}
			},
			customer:{
				info:{
					url: '/homecare/work/mobileNurse/first_customer_info',
					auth: true,
					method: 'GET',
				},
				edit:{
					url: '/homecare/work/mobileNurse/first_customer_edit',
					auth: true,
					method: 'POST',
				}
			},
			location:{
				info:{
					url: '/homecare/work/mobileNurse/first_customer_location_info',
					auth: true,
					method: 'GET',
				},
				set:{
					url: '/homecare/work/mobileNurse/first_customer_location',
					auth: true,
					method: 'POST',
				}
			}
		},
		second:{
			listpg:{
				url: '/homecare/work/mobileNurse/second_listpg',
				auth: true,
				method: 'GET',
			},
			info:{
				url: '/homecare/work/mobileNurse/second_customer_info',
				auth: true,
				method: 'GET',
			},

			submit:{
				url: '/homecare/work/mobileNurse/second_submit',
				auth: true,
				method: 'POST',
			},
			fileData:{
				allList:{
					url: '/homecare/work/mobileNurse/second_data_allList',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/second_data_save',
					auth: true,
					method: 'POST',
				}
			},
			state_info:{
				url: '/homecare/work/mobileNurse/second_state_info',
				auth: true,
				method: 'GET',
			},
			checkin:{
				save:{
					url: '/homecare/work/mobileNurse/second_checkin',
					auth: true,
					method: 'POST',
				},
				info:{
					url: '/homecare/work/mobileNurse/second_checkin_info',
					auth: true,
					method: 'GET',
				},
				range:{
					url: '/homecare/work/mobileNurse/second_checkin_range',
					auth: true,
					method: 'POST',
				},

			},
			scene:{
				setInfo:{
					url: '/homecare/work/mobileNurse/second_photo_setInfo',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/second_photo_save',
					auth: true,
					method: 'POST',
				},
				list:{
					url: '/homecare/work/mobileNurse/second_photo_list',
					auth: true,
					method: 'GET',
				}
			},

			imgHead:{
				info:{
					url: '/homecare/work/mobileNurse/second_customer_imgHead_info',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileNurse/second_customer_imgHead',
					auth: true,
					method: 'POST',
				},
			},
			project:{
				allList:{
					url: '/homecare/work/mobileNurse/project_listBySecondId',
					auth: true,
					method: 'GET',
				},
				list:{
					url: '/homecare/work/mobileNurse/second_project_list',
					auth: true,
					method: 'GET',
				},
				delete:{
					url: '/homecare/work/mobileNurse/second_project_delete',
					auth: true,
					method: 'POST',
				},
				add:{
					url: '/homecare/work/mobileNurse/second_project_add',
					auth: true,
					method: 'POST',
				},
				save:{
					url: '/homecare/work/mobileNurse/second_project_saveAll',
					auth: true,
					method: 'POST',
				},
				time:{
					url: '/homecare/work/mobileNurse/second_project_saveTimes',
					auth: true,
					method: 'POST',
				}
			},
			customer:{
				info:{
					url: '/homecare/work/mobileNurse/second_customer_info',
					auth: true,
					method: 'GET',
				},
				edit:{
					url: '/homecare/work/mobileNurse/second_customer_edit',
					auth: true,
					method: 'POST',
				}
			},
			location:{
				info:{
					url: '/homecare/work/mobileNurse/second_customer_location_info',
					auth: true,
					method: 'GET',
				},
				set:{
					url: '/homecare/work/mobileNurse/second_customer_location',
					auth: true,
					method: 'POST',
				}
			}
		},
		audit:{
			first:{
				listpg:{
					url: '/homecare/work/mobileNurse/first_waitAudit_listpg',
					auth: true,
					method: 'GET',
				}
			},
			second:{
				listpg:{
					url: '/homecare/work/mobileNurse/second_waitAudit_listpg',
					auth: true,
					method: 'GET',
				}
			},
		},
		proofread: {
			listpg: {
				url: '/homecare/work/mobileNurse/proofread_listpg',
				auth: true,
				method: 'GET',
			},
			task: {
				url: '/homecare/work/mobileNurse/proofread_task',
				auth: true,
				method: 'GET',
			},
			save: {
				url: '/homecare/work/mobileNurse/proofread_task_save',
				auth: true,
				method: 'POST',
			},
		},
	},
	care:{
		customer:{
			scheduling:{
				month:{
					url: '/homecare/work/mobileAttendant/scheduling_month_customer',
					auth: true,
					method: 'GET',
				},
				apply:{
					url: '/homecare/work/mobileAttendant/scheduling_apply',
					auth: true,
					method: 'POST',
				},
				applyList:{
					url: '/homecare/work/mobileAttendant/scheduling_change_list',
					auth: true,
					method: 'GET',
				},
				applyCancel:{
					url: '/homecare/work/mobileAttendant/scheduling_change_cancel',
					auth: true,
					method: 'POST',
				},
				audit:{
					listpg:{
						url: '/homecare/work/mobile/scheduling_change_listpg',
						auth: true,
						method: 'GET',
					},
					info:{
						url: '/homecare/work/mobile/change_info',
						auth: true,
						method: 'GET',
					},
					audit:{
						url: '/homecare/work/mobile/change_audit',
						auth: true,
						method: 'POST',
					}
				},
				declare:{
					listpg:{
						url: '/homecare/work/mobileAttendant/work_error_listpg',
						auth: true,
						method: 'GET',
					},
					info:{
						url: '/homecare/work/mobileAttendant/work_error_info',
						auth: true,
						method: 'GET',
					},
					apply:{
						url: '/homecare/work/mobileAttendant/work_error_apply',
						auth: true,
						method: 'POST',
					},
					applyCancel:{
						url: '/homecare/work/mobileAttendant/work_error_applyCancel',
						auth: true,
						method: 'POST',
					},
					fileData:{
						url: '/homecare/work/mobileAttendant/work_error_filedata',
						auth: true,
						method: 'GET',
					},
					project:{
						url: '/homecare/work/mobileAttendant/work_error_project',
						auth: true,
						method: 'GET',
					}
				}
			},
			listpg:{
				url: '/homecare/base/mobileAttendant/my_customer_location_listpg',
				auth: true,
				method: 'GET',
			},
			listpg_waitReceive:{
				url: '/homecare/base/mobileAttendant/my_waitReceive_listpg',
				auth: true,
				method: 'GET',
			},
			listByScheduling:{
				url: '/homecare/work/mobileAttendant/scheduling_customer_list',
				auth: true,
				method: 'GET',
			},
			info:{
				url: '/homecare/base/mobileAttendant/customer_info',
				auth: true,
				method: 'GET',
			},
			fileData:{
				allList:{
					url: '/homecare/base/mobileAttendant/data_allList',
					auth: true,
					method: 'GET',
				}
			},
			receive:{
				url: '/homecare/base/mobileAttendant/receive',
				auth: true,
				method: 'GET',
			},
			workState:{
				url: '/homecare/work/mobileAttendant/customer_work_state',
				auth: true,
				method: 'GET',
			},
			location:{
				info:{
					url: '/homecare/base/mobileAttendant/customer_location_info',
					auth: true,
					method: 'GET',
				},
				apply:{
					url: '/homecare/base/mobileAttendant/customer_location_apply',
					auth: true,
					method: 'POST',
				}
			},
			locationSave:{
				url: '/homecare/base/mobileAttendant/customer_location_save',
				auth: true,
				method: 'POST',
			},
		},
		myLocation:{
			info:{
				url: '/homecare/base/mobileAttendant/my_location_info',
				auth: true,
				method: 'GET',
			},
			save:{
				url: '/homecare/base/mobileAttendant/my_location_save',
				auth: true,
				method: 'POST',
			},
		},
		work:{
			listpg:{
				url: '/homecare/work/mobileAttendant/work_listpg',
				auth: true,
				method: 'GET',
			},
			allInfo:{
				url: '/homecare/work/mobileAttendant/work_AllInfo',
				auth: true,
				method: 'GET',
			},
			info:{
				url: '/homecare/work/mobileAttendant/work_info',
				auth: true,
				method: 'GET',
			},
			start:{
				url: '/homecare/work/mobileAttendant/work_create',
				auth: true,
				method: 'POST',
			},
			cancel:{
				url: '/homecare/work/mobileAttendant/work_cancel',
				auth: true,
				method: 'GET',
			},
			state:{
				url: '/homecare/work/mobileAttendant/work_state_info',
				auth: true,
				method: 'GET',
			},
			dataFile:{
				allList:{
					url: '/homecare/work/mobileAttendant/work_data_allList',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileAttendant/work_data_save',
					auth: true,
					method: 'POST',
				},
			},
			checkin:{
				save:{
					url: '/homecare/work/mobileAttendant/work_checkin',
					auth: true,
					method: 'POST',
				},
				info:{
					url: '/homecare/work/mobileAttendant/work_checkin_info',
					auth: true,
					method: 'GET',
				},
				range:{
					url: '/homecare/work/mobileAttendant/work_checkin_range',
					auth: true,
					method: 'POST',
				},
			},
			checkout:{
				save:{
					url: '/homecare/work/mobileAttendant/work_checkout',
					auth: true,
					method: 'POST',
				},
				info:{
					url: '/homecare/work/mobileAttendant/work_checkout_info',
					auth: true,
					method: 'GET',
				},
				range:{
					url: '/homecare/work/mobileAttendant/work_checkout_inRange',
					auth: true,
					method: 'GET',
				}
			},
			scene:{
				setInfo:{
					url: '/homecare/work/mobileAttendant/work_photo_setInfo',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileAttendant/work_photo_save',
					auth: true,
					method: 'POST',
				},
				list:{
					url: '/homecare/work/mobileAttendant/work_photo_list',
					auth: true,
					method: 'GET',
				}
			},
			imgHead:{
				info:{
					url: '/homecare/work/mobileAttendant/work_customer_imgHead_info',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileAttendant/work_customer_imgHead',
					auth: true,
					method: 'POST',
				},
			},
			project:{
				allList:{
					url: '/homecare/work/mobileAttendant/work_allProject_listpg',
					auth: true,
					method: 'GET',
				},
				allRuleListpg:{
					url: '/homecare/work/mobileAttendant/work_allProject_rule_listpg',
					auth: true,
					method: 'GET',
				},
				listpg:{
					url: '/homecare/work/mobileAttendant/work_project_listpg',
					auth: true,
					method: 'GET',
				},
				ruleListpg:{
					url: '/homecare/work/mobileAttendant/work_project_rule_listpg',
					auth: true,
					method: 'GET',
				},
				delete:{
					url: '/homecare/work/mobileAttendant/work_project_delete',
					auth: true,
					method: 'POST',
				},
				save:{
					url: '/homecare/work/mobileAttendant/work_project_add',
					auth: true,
					method: 'POST',
				}
			},
			submit:{
				url: '/homecare/work/mobileAttendant/work_submit',
				auth: true,
				method: 'POST',
			},
			myListpg:{
				url: '/homecare/work/mobileAttendant/work_listpg',
				auth: true,
				method: 'GET',
			},
			customerListpg:{
				url: '/homecare/work/mobileAttendant/customer_work_listpg',
				auth: true,
				method: 'GET',
			},
		},
		proofread: {
			listpg: {
				url: '/homecare/work/mobileAttendant/proofread_listpg',
				auth: true,
				method: 'GET',
			},
			task: {
				url: '/homecare/work/mobileAttendant/proofread_task',
				auth: true,
				method: 'GET',
			},
			save: {
				url: '/homecare/work/mobileAttendant/proofread_task_save',
				auth: true,
				method: 'POST',
			},
		},
	},
	person:{
		listpg:{
			url: '/homecare/work/mobileSales/noGovAudit_listpg',
			auth: true,
			method: 'GET',
		},
		noAuditListpg:{
			url: '/homecare/work/mobileSales/submit_noGovAudit_listpg',
			auth: true,
			method: 'GET',
		},
		audit_listpg:{
			url: '/homecare/work/mobileSales/submit_govAudit_listpg',
			auth: true,
			method: 'GET',
		},
		info:{
			url: '/homecare/work/mobileSales/info',
			auth: true,
			method: 'GET',
		},
		save:{
			url: '/homecare/work/mobileSales/save',
			auth: true,
			method: 'POST',
		},
		delete:{
			url: '/homecare/work/mobileSales/delete',
			auth: true,
			method: 'POST',
		},
		savePhoto:{
			url: '/homecare/work/mobileSales/savePhoto',
			auth: true,
			method: 'POST',
		},
		submit:{
			url: '/homecare/work/mobileSales/submit',
			auth: true,
			method: 'POST',
		},
		submitCancel:{
			url: '/homecare/work/mobileSales/submitCancel',
			auth: true,
			method: 'POST',
		},
		audit:{
			url: '/homecare/work/mobileSales/gov_audit',
			auth: true,
			method: 'POST',
		},
		auditCancel:{
			url: '/homecare/work/mobileSales/gov_auditCancel',
			auth: true,
			method: 'POST',
		},
		fileData:{
			allList:{
				url: '/homecare/work/mobileSales/data_allList',
				auth: true,
				method: 'GET',
			},
			save:{
				url: '/homecare/work/mobileSales/data_save',
				auth: true,
				method: 'POST',
			},
		},
		gov:{
			listpg:{
				url: '/homecare/work/mobileSales/govAuditPass_listpg',
				auth: true,
				method: 'GET',
			},
			audit:{
				url: '/homecare/work/mobileSales/gov_auditPass',
				auth: true,
				method: 'POST',
			},
			auditCancel:{
				url: '/homecare/work/mobileSales/gov_auditPassCancel',
				auth: true,
				method: 'POST',
			},
			customerInfo:{
				url: '/homecare/work/mobileSales/customer_info',
				auth: true,
				method: 'GET',
			},
			groupListpg:{
				url: '/homecare/base/mobileSalesman/group_listpg',
				auth: true,
				method: 'GET',
			},
			distributionGroup:{
				url: '/homecare/work/mobileSales/distributionGroup',
				auth: true,
				method: 'POST',
			},
			fileData:{
				allList:{
					url: '/homecare/work/mobileSales/gov_data_allList',
					auth: true,
					method: 'GET',
				},
				save:{
					url: '/homecare/work/mobileSales/gov_data_save',
					auth: true,
					method: 'POST',
				},
			},
		},
		myCustomer:{
			url: '/homecare/base/mobileSalesman/my_customer_location_listpg',
			auth: true,
			method: 'GET',
		}
	},
}
