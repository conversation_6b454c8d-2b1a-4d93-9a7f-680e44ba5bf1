{
	"easycom": {
		"^ut-(.*)": "@/components/ut/ut-$1/ut-$1.vue",
		"^u-(.*)": "@/components/uview-ui/components/u-$1/u-$1.vue",
		"loading": "@/components/loading/loading.vue",
		"f-navbar": "@/components/f-navbar/f-navbar.vue",
		"list-cell": "@/components/list-cell/list-cell.vue"
	},
	"lazyCodeLoading": "requiredComponents",
	"pages": [{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"app-plus": {
					"titleNView": false,
					"bounce": "none",
					"scrollIndicator": "none"
				},
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/user/index",
			"style": {
				"app-plus": {
					"titleNView": false,
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/public/webview",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/outlet/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/customer/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"app-plus": {
					"titleNView": false
				}
			}
		}
	],
	"subPackages": [
		{
			"root": "pagesB",
			"pages": [
				{
					"path": "upload/camera"
				},
				{
					// 后台管理主页
					"path": "index/index"
				},
				{
					// 鉴权
					"path": "check/check"
				}
			]
		},
		{
			"root": "pagesA",
			"pages": [
				{
					"path": "index/index"
				},
				{
					"path": "nurse/customer/index"
				},{
					"path": "nurse/customer/info"
				},{
					"path": "nurse/attendant/index"
				},{
					"path": "nurse/attendant/customer-list"
				},{
					"path": "nurse/assign-first/index"
				},{
					"path": "nurse/assign-first/first"
				},{
					"path": "nurse/assign-first/position"
				},{
					"path": "nurse/assign-first/scene-photo"
				},{
					"path": "nurse/assign-first/file-data"
				},{
					"path": "nurse/assign-first/project"
				},{
					"path": "nurse/assign-first/img-head"
				},{
					"path": "nurse/assign-first/save"
				},{
					"path": "nurse/assign-first/location"
				},{
					"path": "nurse/assign-second/index"
				},{
					"path": "nurse/assign-second/second"
				},{
					"path": "nurse/assign-second/position"
				},{
					"path": "nurse/assign-second/scene-photo"
				},{
					"path": "nurse/assign-second/file-data"
				},{
					"path": "nurse/assign-second/project"
				},{
					"path": "nurse/assign-second/img-head"
				},{
					"path": "nurse/assign-second/save"
				},{
					"path": "nurse/assign-second/location"
				},{
					"path": "nurse/set-attendant/index"
				},{
					"path": "nurse/set-attendant/set"
				},{
					"path": "nurse/change-attendant/index"
				},{
					"path": "nurse/change-attendant/change"
				},{
					"path": "nurse/audit/first"
				},{
					"path": "nurse/audit/first-audit"
				},{
					"path": "nurse/audit/second"
				},{
					"path": "nurse/audit/second-audit"
				},{
					"path": "nurse/nurse-proofread/index"
				},{
					"path": "nurse/nurse-proofread/check"
				},{
					"path": "care/customer/index"
				},{
					"path": "care/customer/info"
				},{
					"path": "care/customer/care"
				},{
					"path": "care/customer/location"
				},{
					"path": "care/care/index"
				},{
					"path": "care/care/care"
				},{
					"path": "care/care/location"
				},{
					"path": "care/work/start"
				},{
					"path": "care/work/position"
				},{
					"path": "care/work/scene-photo"
				},{
					"path": "care/work/project"
				},{
					"path": "care/work/img-head"
				},{
					"path": "care/my-work/index"
				},{
					"path": "care/info/index"
				},{
					"path": "care/history/index"
				},{
					"path": "care/history/info"
				},{
					"path": "care/new-customer/index"
				},{
					"path": "care/new-customer/info"
				},{
					"path": "person/my-apply/index"
				},{
					"path": "person/my-apply/info"
				},{
					"path": "person/my-apply/save"
				},{
					"path": "person/my-apply/file-data"
				},{
					"path": "person/my-apply/img-head"
				},{
					"path": "person/submit/index"
				},{
					"path": "person/submit/info"
				},{
					"path": "person/audit/index"
				},{
					"path": "person/audit/info"
				},{
					"path": "person/audit/file-data"
				},{
					"path": "person/set-attendant/index"
				},{
					"path": "person/set-attendant/info"
				},{
					"path": "person/my-customer/index"
				},{
					"path": "person/my-customer/info"
				},{
					"path": "canteen/account/index"
				},{
					"path": "canteen/account/edit"
				},{
					"path": "canteen/deposit/index"
				},{
					"path": "canteen/deposit/deposit"
				},{
					"path": "canteen/refund/index"
				},{
					"path": "canteen/refund/refund"
				},{
					"path": "canteen/consume-list/index"
				},{
					"path": "index/cpu-card/init"
				},{
					"path": "index/cpu-card/restore"
				},{
					"path": "care/scheduling/apply/list"
				},{
					"path": "care/scheduling/audit/index"
				},{
					"path": "care/scheduling/declare/index"
				},{
					"path": "care/scheduling/declare/apply"
				},{
					"path": "care/team-proofread/index"
				},{
					"path": "care/team-proofread/check"
				}
			]
		},
		{
			"root":"pagesC",
			"pages":[{ //代办问诊
				"path": "agent/index"
			},
			{ //医院服务选择
				"path": "hospital/hospital"
			},
			{ // 医院服务订单填写
				"path": "hospital/subscribe/index"
			},
			{ // 订单支付
				"path": "hospital/subscribe/pay"
			},
			{ // 订单支付成功
				"path": "hospital/subscribe/pay-success"
			},
			{ // 注册成陪诊师
				"path": "hospital/register"
			},
			{ // 陪诊师资料
				"path": "hospital/registerInfo"
			},
			{ // 我的陪诊订单
				"path": "work/my-work-order"
			},
			{ // 基础项目信息
				"path": "project/base-project-order"
			},
			{ // 基础项目下单信息
				"path": "project/order-refund"
			}]
		}
	],
	"preloadRule":{
		"pages/customer/index":{
			"network":"all",
			"packages": [
				"pagesA"
			]
		}
	},
	"style": "v2",
	// "sitemapLocation": "sitemap.json",
	"tabBar": {
		"backgroundColor": "#ffffff",
		"borderStyle": "white",
		"selectedColor": "#3B7A86",
		"color": "#666666",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "/static/theme/home.png",
				"selectedIconPath": "/static/theme/home-olive.png",
				"text": "首页"
			}, {
				"pagePath": "pages/outlet/index",
				"iconPath": "/static/theme/order.png",
				"selectedIconPath": "/static/theme/order-olive.png",
				"text": "陪诊"
			}, {
				"pagePath": "pages/customer/index",
				"iconPath": "/static/theme/ut.png",
				"selectedIconPath": "/static/theme/ut-olive.png",
				"text": "康养"
			}, {
				"pagePath": "pages/user/index",
				"iconPath": "/static/theme/user.png",
				"selectedIconPath": "/static/theme/user-olive.png",
				"text": "我的"
			}
		]
	},
	"permission": {
		"scope.userLocation": {
			"desc": "你的位置信息将用于小程序位置接口的效果展示"
		}
	},
	"globalStyle": {
		"navigationStyle": "custom", // black
		"backgroundTextStyle": "light",
		"navigationBarTitleText": "先施康养",
		"pageOrientation": "auto",
		"scrollIndicator": "none",
		"app-plus": {
			"titleNView":false,
			"bounce": "none",
			"scrollIndicator": "none"
		}
	}
}
